/**
 * Device Farm Status Reporter
 * Handles test status reporting to Appium Device Farm
 */

import axios from 'axios';

export interface TestResult {
  testName: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: string;
  timestamp: number;
}

export interface SessionResult {
  sessionId: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  overallStatus: 'passed' | 'failed';
  duration: number;
  timestamp: number;
}

export class DeviceFarmReporter {
  private static readonly DEVICE_FARM_CONFIG = {
    accessKey: 'admin_AzZvGSzPbIrrEx',
    token: '9de28a88-21b5-4887-8282-848106f3afe5',
    hostname: 'localhost',
    port: 4723,
    baseUrl: 'http://localhost:4723'
  };

  private static sessionResults: TestResult[] = [];
  private static sessionStartTime: number = Date.now();

  /**
   * Report individual test result to device farm
   */
  static async reportTestResult(result: TestResult): Promise<void> {
    try {
      console.log(`📤 Reporting test result to device farm: ${result.testName} - ${result.status}`);
      
      // Store result for session summary
      this.sessionResults.push(result);

      // Send to device farm API if available
      await this.sendToDeviceFarm('/test-result', result);
      
      console.log(`✅ Test result reported successfully: ${result.testName}`);
    } catch (error) {
      console.error(`❌ Failed to report test result for ${result.testName}:`, error);
    }
  }

  /**
   * Report session completion to device farm
   */
  static async reportSessionComplete(sessionId: string): Promise<void> {
    try {
      const sessionDuration = Date.now() - this.sessionStartTime;
      const totalTests = this.sessionResults.length;
      const passedTests = this.sessionResults.filter(r => r.status === 'passed').length;
      const failedTests = this.sessionResults.filter(r => r.status === 'failed').length;
      const skippedTests = this.sessionResults.filter(r => r.status === 'skipped').length;
      
      const sessionResult: SessionResult = {
        sessionId,
        totalTests,
        passedTests,
        failedTests,
        skippedTests,
        overallStatus: failedTests === 0 ? 'passed' : 'failed',
        duration: sessionDuration,
        timestamp: Date.now()
      };

      console.log(`📊 Reporting session completion to device farm:`);
      console.log(`   Session ID: ${sessionId}`);
      console.log(`   Total Tests: ${totalTests}`);
      console.log(`   Passed: ${passedTests}`);
      console.log(`   Failed: ${failedTests}`);
      console.log(`   Overall Status: ${sessionResult.overallStatus.toUpperCase()}`);

      // Send to device farm API
      await this.sendToDeviceFarm('/session-complete', sessionResult);
      
      // Log completion marker for device farm to detect
      console.log(`🏁 DEVICE_FARM_SESSION_COMPLETE: ${sessionResult.overallStatus.toUpperCase()}`);
      console.log(`📈 DEVICE_FARM_SUCCESS_RATE: ${totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(2) : 0}%`);
      
      console.log(`✅ Session completion reported successfully`);
    } catch (error) {
      console.error(`❌ Failed to report session completion:`, error);
    }
  }

  /**
   * Log status for device farm detection (no API calls)
   */
  private static async sendToDeviceFarm(endpoint: string, data: any): Promise<void> {
    // Instead of making API calls, use structured logging that device farm can parse
    console.log(`📡 DEVICE_FARM_LOG: ${endpoint}`);
    console.log(`📊 DEVICE_FARM_DATA: ${JSON.stringify(data)}`);

    // Device farm specific status markers
    if (endpoint === '/test-result') {
      console.log(`🔍 DF_TEST_RESULT: ${data.testName} = ${data.status.toUpperCase()}`);
      if (data.error) {
        console.log(`💥 DF_TEST_ERROR: ${data.error}`);
      }
    } else if (endpoint === '/session-complete') {
      console.log(`🏁 DF_SESSION_STATUS: ${data.overallStatus.toUpperCase()}`);
      console.log(`📈 DF_TEST_SUMMARY: ${data.passedTests}/${data.totalTests} passed`);
    }
  }

  /**
   * Reset session data for new test run
   */
  static resetSession(): void {
    this.sessionResults = [];
    this.sessionStartTime = Date.now();
    console.log(`🔄 Device farm reporter session reset`);
  }

  /**
   * Get current session statistics
   */
  static getSessionStats(): {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    skippedTests: number;
    duration: number;
  } {
    const totalTests = this.sessionResults.length;
    const passedTests = this.sessionResults.filter(r => r.status === 'passed').length;
    const failedTests = this.sessionResults.filter(r => r.status === 'failed').length;
    const skippedTests = this.sessionResults.filter(r => r.status === 'skipped').length;
    const duration = Date.now() - this.sessionStartTime;

    return {
      totalTests,
      passedTests,
      failedTests,
      skippedTests,
      duration
    };
  }
}
