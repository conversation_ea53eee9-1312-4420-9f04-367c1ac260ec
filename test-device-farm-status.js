#!/usr/bin/env node

/**
 * Test script to verify device farm status reporting
 * Run this to check if the status reporting mechanisms are working
 */

const { DeviceFarmReporter } = require('./dist/src/services/device-farm-reporter');

async function testDeviceFarmReporting() {
  console.log('🧪 Testing Device Farm Status Reporting...\n');

  try {
    // Reset session
    DeviceFarmReporter.resetSession();
    console.log('✅ Session reset successful');

    // Test successful test result
    await DeviceFarmReporter.reportTestResult({
      testName: 'Sample Test - Login Flow',
      status: 'passed',
      duration: 15000,
      timestamp: Date.now()
    });
    console.log('✅ Passed test result reported');

    // Test failed test result
    await DeviceFarmReporter.reportTestResult({
      testName: 'Sample Test - Checkout Flow',
      status: 'failed',
      duration: 8000,
      error: 'Element not found: checkout-button',
      timestamp: Date.now()
    });
    console.log('✅ Failed test result reported');

    // Get session stats
    const stats = DeviceFarmReporter.getSessionStats();
    console.log('\n📊 Session Statistics:');
    console.log(`   Total Tests: ${stats.totalTests}`);
    console.log(`   Passed: ${stats.passedTests}`);
    console.log(`   Failed: ${stats.failedTests}`);
    console.log(`   Duration: ${stats.duration}ms`);

    // Test session completion
    await DeviceFarmReporter.reportSessionComplete('test-session-123');
    console.log('✅ Session completion reported');

    console.log('\n🎉 Device Farm Status Reporting Test Completed Successfully!');
    console.log('\nIf you see the above messages without errors, the status reporting should work.');
    console.log('The "unmarked" status issue should be resolved with these changes.');

  } catch (error) {
    console.error('❌ Device Farm Status Reporting Test Failed:', error);
    process.exit(1);
  }
}

// Run the test
testDeviceFarmReporting();
