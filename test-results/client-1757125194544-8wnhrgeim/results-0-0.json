{"start": "2025-09-06T02:20:10.973Z", "end": "2025-09-06T02:20:25.863Z", "capabilities": {"platformName": "Android", "df:recordVideo": true, "df:videoQuality": "high", "df:videoFPS": 15, "df:recordVideoOnFailureOnly": false, "df:videoName": "test_1757125197114", "df:videoTimeLimit": null, "df:options": {"saveDeviceLogs": true, "build": "test_1757125197114", "accesskey": "admin_AzZvGSzPbIrrEx", "token": "9de28a88-21b5-4887-8282-848106f3afe5", "recordVideo": true, "videoQuality": "high", "videoFPS": 15, "recordVideoOnFailureOnly": false, "videoName": "test_1757125197114", "videoTimeLimit": null, "options": {"saveDeviceLogs": true, "build": "test_1757125197114", "accesskey": "admin_AzZvGSzPbIrrEx", "token": "9de28a88-21b5-4887-8282-848106f3afe5", "recordVideo": true, "videoQuality": "high", "videoFPS": 15, "recordVideoOnFailureOnly": false, "videoName": "test_1757125197114", "videoTimeLimit": null}, "enableTestStatusReporting": true, "testResultsEndpoint": "/test-results", "logTestResults": true, "markTestStatus": true}, "df:enableTestStatusReporting": true, "df:testResultsEndpoint": "/test-results", "df:logTestResults": true, "df:markTestStatus": true, "platformVersion": "16", "deviceName": "emulator-5554", "automationName": "UiAutomator2", "newCommandTimeout": 300, "sessionOverride": true, "autoGrantPermissions": true, "noReset": false, "fullReset": false, "appWaitActivity": "*", "appWaitDuration": 30000, "androidInstallTimeout": 120000, "uiautomator2ServerInstallTimeout": 60000, "uiautomator2ServerLaunchTimeout": 60000, "skipServerInstallation": false, "skipDeviceInitialization": false, "disableWindowAnimation": false, "skipUnlock": true, "androidDeviceReadyTimeout": 60, "appWaitForLaunch": true, "app": "/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_mobile_single_test/client-apps/session-41d09021-1a6c-4a13-8a1a-edee6554ba97/1756047271343-Android.SauceLabs.Mobile.Sample.app.2.7.1.apk", "udid": "emulator-5554", "systemPort": 50986, "chromeDriverPort": 50987, "adbPort": 5037, "mjpegServerPort": 50988, "platform": "LINUX", "webStorageEnabled": false, "takesScreenshot": true, "javascriptEnabled": true, "databaseEnabled": false, "networkConnectionEnabled": true, "locationContextEnabled": false, "warnings": {}, "desired": {"platformName": "Android", "df:recordVideo": true, "df:videoQuality": "high", "df:videoFPS": 15, "df:recordVideoOnFailureOnly": false, "df:videoName": "test_1757125197114", "df:videoTimeLimit": null, "df:options": {"saveDeviceLogs": true, "build": "test_1757125197114", "accesskey": "admin_AzZvGSzPbIrrEx", "token": "9de28a88-21b5-4887-8282-848106f3afe5", "recordVideo": true, "videoQuality": "high", "videoFPS": 15, "recordVideoOnFailureOnly": false, "videoName": "test_1757125197114", "videoTimeLimit": null, "options": {"saveDeviceLogs": true, "build": "test_1757125197114", "accesskey": "admin_AzZvGSzPbIrrEx", "token": "9de28a88-21b5-4887-8282-848106f3afe5", "recordVideo": true, "videoQuality": "high", "videoFPS": 15, "recordVideoOnFailureOnly": false, "videoName": "test_1757125197114", "videoTimeLimit": null}, "enableTestStatusReporting": true, "testResultsEndpoint": "/test-results", "logTestResults": true, "markTestStatus": true}, "df:enableTestStatusReporting": true, "df:testResultsEndpoint": "/test-results", "df:logTestResults": true, "df:markTestStatus": true, "platformVersion": "16", "deviceName": "Medium_Phone_API_36.0", "automationName": "UiAutomator2", "newCommandTimeout": 300, "sessionOverride": true, "autoGrantPermissions": true, "noReset": false, "fullReset": false, "appWaitActivity": "*", "appWaitDuration": 30000, "androidInstallTimeout": 120000, "uiautomator2ServerInstallTimeout": 60000, "uiautomator2ServerLaunchTimeout": 60000, "skipServerInstallation": false, "skipDeviceInitialization": false, "disableWindowAnimation": false, "skipUnlock": true, "androidDeviceReadyTimeout": 60, "appWaitForLaunch": true, "app": "/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_mobile_single_test/client-apps/session-41d09021-1a6c-4a13-8a1a-edee6554ba97/1756047271343-Android.SauceLabs.Mobile.Sample.app.2.7.1.apk", "udid": "emulator-5554", "systemPort": 50986, "chromeDriverPort": 50987, "adbPort": 5037, "mjpegServerPort": 50988}, "deviceUDID": "emulator-5554", "appPackage": "com.swaglabsmobileapp", "appActivity": "com.swaglabsmobileapp.SplashActivity", "pixelRatio": "2.625", "statBarHeight": 63, "viewportRect": {"left": 0, "top": 63, "width": 1080, "height": 2337}, "deviceApiLevel": 36, "deviceManufacturer": "Google", "deviceModel": "sdk_gphone64_arm64", "deviceScreenSize": "1080x2400", "deviceScreenDensity": 420, "sessionId": "58bf7268-f0c7-436b-b2d4-262a51ba5987"}, "framework": "mocha", "mochaOpts": {"timeout": 120000, "ui": "bdd"}, "suites": [{"name": "Test-client-1757125194544-8wnhrgeim", "duration": 13253, "start": "2025-09-06T02:20:10.976Z", "end": "2025-09-06T02:20:24.229Z", "sessionId": "58bf7268-f0c7-436b-b2d4-262a51ba5987", "tests": [{"name": "should execute mobile test steps", "start": "2025-09-06T02:20:10.976Z", "end": "2025-09-06T02:20:24.224Z", "duration": 13248, "state": "passed"}], "hooks": []}], "specs": ["file:///Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_mobile_single_test/tests/master.spec.ts"], "state": {"passed": 1, "failed": 0, "skipped": 0}}