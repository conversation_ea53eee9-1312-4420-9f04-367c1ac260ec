/**
 * Fixed Mobile Test Execution
 * Addresses blank screen video recording issues
 */

import { CapabilityFactory } from '../src/services/capability-factory';

describe('Fixed Mobile Test Execution', () => {
  it('should execute mobile test steps with proper app launch and video recording', async function() {
    this.timeout(300000); // 5 minutes timeout

    try {
      console.log('🚀 Starting fixed mobile test execution');

      // Get test data from environment
      const testData = process.env.TEST_DATA;
      if (!testData) {
        throw new Error('TEST_DATA environment variable is required');
      }

      const stepsData = JSON.parse(testData);
      console.log(`📱 Loaded test data with ${stepsData.steps.length} steps`);

      // Find setup action
      let setupAction = null;
      for (const step of stepsData.steps) {
        const actions = JSON.parse(step.Actions);
        const setup = actions.find((action: any) => action.action === 'setup');
        if (setup) {
          setupAction = setup;
          break;
        }
      }

      if (!setupAction) {
        throw new Error('No setup action found in test steps');
      }

      console.log('📱 Found setup action, verifying app launch');

      // Verify browser session is available
      if (typeof browser === 'undefined') {
        throw new Error('Browser session not available - make sure this runs within WebDriverIO context');
      }

      console.log(`🔍 Session ID: ${browser.sessionId}`);
      console.log(`🔍 Browser capabilities: ${JSON.stringify(browser.capabilities, null, 2)}`);

      // Wait for app to be fully loaded and visible
      console.log('⏱️ Waiting for app to fully load...');
      await browser.pause(8000); // Longer wait for app startup

      // Verify app is actually running by checking page source
      let appLoaded = false;
      let retries = 0;
      const maxRetries = 5;

      while (!appLoaded && retries < maxRetries) {
        try {
          console.log(`🔍 Checking app status (attempt ${retries + 1}/${maxRetries})...`);
          const source = await browser.getPageSource();
          
          if (source && source.length > 1000) {
            console.log(`📱 App loaded successfully, page source length: ${source.length} characters`);
            
            // Check for common app elements
            if (source.includes('Username') || source.includes('test-Username') || 
                source.includes('Password') || source.includes('LOGIN')) {
              console.log('✅ Login screen detected - app is ready for testing');
              appLoaded = true;
            } else {
              console.log('⚠️ App loaded but login screen not detected, checking for other elements...');
              // App might be on a different screen, still consider it loaded
              appLoaded = true;
            }
          } else {
            console.log('⚠️ App not fully loaded yet, retrying...');
            await browser.pause(3000);
            retries++;
          }
        } catch (error) {
          console.log(`⚠️ Error checking app status: ${error}`);
          await browser.pause(3000);
          retries++;
        }
      }

      if (!appLoaded) {
        throw new Error('App failed to load properly after multiple attempts');
      }

      // Take a screenshot to verify app is visible
      try {
        const screenshotPath = `./screenshots/app-loaded-${Date.now()}.png`;
        await browser.saveScreenshot(screenshotPath);
        console.log(`📸 Screenshot saved: ${screenshotPath}`);
      } catch (error) {
        console.log('⚠️ Could not take screenshot:', error);
      }

      // Execute test steps (skip setup step)
      console.log('🔍 Filtering test steps...');
      const testSteps = stepsData.steps.filter((step: any) => {
        const actions = JSON.parse(step.Actions);
        return !actions.some((action: any) => action.action === 'setup');
      });

      console.log(`📋 ===== STARTING TEST STEP EXECUTION =====`);
      console.log(`📋 Total steps: ${stepsData.steps.length}, Test steps (excluding setup): ${testSteps.length}`);

      for (const step of testSteps) {
        console.log(`\n🔥 ===== EXECUTING STEP ${step.step}: ${step.stepName} =====`);

        const actions = JSON.parse(step.Actions);
        console.log(`🔍 Actions in this step: ${actions.length}`);
        
        for (const action of actions) {
          console.log(`🎯 Action: ${action.action}, Target: ${action.target}, Value: ${action.value}`);
          
          if (action.action === 'write' && action.target && action.value) {
            console.log(`⌨️ Writing "${action.value}" to element "${action.target}"`);
            
            try {
              // Use accessibility ID selector
              const element = browser.$(`~${action.target.replace('~', '')}`);
              
              console.log('⏳ Waiting for element to exist...');
              await element.waitForExist({ timeout: 15000 });
              
              console.log('✅ Element found, clearing and setting value...');
              await element.clearValue();
              await element.setValue(action.value);
              
              console.log(`✅ Successfully wrote "${action.value}" to "${action.target}"`);
              
              // Take screenshot after each action for debugging
              await browser.saveScreenshot(`./screenshots/after-write-${step.step}-${Date.now()}.png`);
              
            } catch (error) {
              console.error(`❌ Failed to write to element "${action.target}":`, error);
              // Take screenshot on error
              await browser.saveScreenshot(`./screenshots/error-write-${step.step}-${Date.now()}.png`);
              throw error;
            }
            
          } else if (action.action === 'click' && action.target) {
            console.log(`🖱️ Clicking element "${action.target}"`);
            
            try {
              // Use accessibility ID selector
              const element = browser.$(`~${action.target.replace('~', '')}`);
              
              console.log('⏳ Waiting for element to exist...');
              await element.waitForExist({ timeout: 15000 });
              
              console.log('✅ Element found, clicking...');
              await element.click();
              
              console.log(`✅ Successfully clicked "${action.target}"`);
              
              // Take screenshot after each action for debugging
              await browser.saveScreenshot(`./screenshots/after-click-${step.step}-${Date.now()}.png`);
              
            } catch (error) {
              console.error(`❌ Failed to click element "${action.target}":`, error);
              // Take screenshot on error
              await browser.saveScreenshot(`./screenshots/error-click-${step.step}-${Date.now()}.png`);
              throw error;
            }
            
          } else if (action.action === 'prompt') {
            console.log(`💬 Prompt: ${action.value || action.prompt}`);
            // Handle prompt actions if needed
            
          } else {
            console.log(`⚠️ Unknown action: ${action.action}`);
          }
          
          // Longer delay between actions for better video recording
          await browser.pause(2000);
        }
      }

      console.log('\n🎉 All test steps completed successfully!');

      // Final screenshot
      await browser.saveScreenshot(`./screenshots/test-completed-${Date.now()}.png`);

      // Additional verification
      try {
        const currentActivity = await browser.getCurrentActivity();
        console.log(`📱 Current activity after test: ${currentActivity}`);
      } catch (error) {
        console.log('⚠️ Could not get current activity:', error);
      }

      // Explicit test completion marker for device farm
      console.log('🏁 TEST_EXECUTION_COMPLETE: SUCCESS');
      console.log('✅ DEVICE_FARM_TEST_STATUS: PASSED');
      
    } catch (error) {
      console.error('❌ Test execution failed:', error);

      // Take error screenshot
      try {
        await browser.saveScreenshot(`./screenshots/test-failed-${Date.now()}.png`);
      } catch (screenshotError) {
        console.log('⚠️ Could not take error screenshot:', screenshotError);
      }

      // Explicit test failure marker for device farm
      console.log('🏁 TEST_EXECUTION_COMPLETE: FAILURE');
      console.log('❌ DEVICE_FARM_TEST_STATUS: FAILED');
      console.log(`💥 DEVICE_FARM_ERROR: ${error}`);

      throw error;
    }
  });
});
