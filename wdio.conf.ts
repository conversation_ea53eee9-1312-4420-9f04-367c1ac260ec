import type { Options } from '@wdio/types'
import { CapabilityFactory } from './src/services/capability-factory'
import * as fs from 'fs'
import * as path from 'path'
import axios from 'axios'
// The 'browser' object is available globally in WebdriverIO test hooks

export const config: WebdriverIO.Config = {
    //
    // ====================
    // Runner Configuration
    // ====================
    //
    // ==================
    // Specify Test Files
    // ==================
    specs: [
        './tests/fixed-mobile-test.spec.ts'  // Use the fixed test file
    ],
    
    // Patterns to exclude.
    exclude: [
        // 'path/to/excluded/files'
    ],
    maxInstances: 1,
    
    //
    // ============
    // Capabilities
    // ============
    // Choose one of the following capability configurations based on your needs:
    
    // Option 1: Android Mobile Testing
    // Dynamic capabilities based on TEST_DATA environment variable
    capabilities: (() => {
        const testDataEnv = process.env.TEST_DATA;

        if (testDataEnv) {
            try {
                const testData = JSON.parse(testDataEnv);
                // console.log('🔧 wdio.conf.ts: Generating dynamic capabilities from test data');
                // console.log('🔍 wdio.conf.ts: Test data structure:', JSON.stringify(testData, null, 2));

                const dynamicCapabilities = CapabilityFactory.createCapabilities(testData.steps || []);

                if (dynamicCapabilities && CapabilityFactory.isMobilePlatform(dynamicCapabilities)) {
                    // console.log('📱 wdio.conf.ts: Using mobile capabilities');
                    // Return mobile capabilities in the format expected by wdio
                    return [{
                        ...dynamicCapabilities.capabilities,
                        // Add connection details for remote Appium server
                        hostname: dynamicCapabilities.hostname,
                        port: dynamicCapabilities.port,
                        path: dynamicCapabilities.path
                    }];
                }
            } catch (error) {
                console.error('🚫 wdio.conf.ts: Failed to parse TEST_DATA:', error);
            }
        }

        // Fallback: Return error capability to indicate mobile-only mode
        console.log('🚫 wdio.conf.ts: No valid mobile capabilities found - mobile testing required');
        return [{
            // This will cause the test to fail with a clear message
            platformName: 'MobileRequired',
            'appium:deviceName': 'Mobile testing only - setup action required'
        }];
    })(),

    //
    // ===================
    // Test Configurations
    // ===================
    logLevel: 'info',
    bail: 0,
    waitforTimeout: 10000,
    connectionRetryTimeout: 120000,
    connectionRetryCount: 0,

    // Appium server configuration for mobile testing
    hostname: 'localhost',
    port: 4723,
    path: '/wd/hub',
    
    runner: 'local',

    //
    // Test runner services
    // Disable appium service since we're using external Appium server
    services: [],
    
    //
    // Framework you want to run your specs with.
    framework: 'mocha',
    
    //
    // The number of times to retry the entire specfile when it fails as a whole
    specFileRetries: 1, // Increased for mobile stability
    
    //
    // Delay in seconds between the spec file retry attempts
    specFileRetriesDelay: 5, // Added delay for mobile
    
    //
    // Whether or not retried spec files should be retried immediately or deferred to the end of the queue
    specFileRetriesDeferred: false,
    
    //
    // Test reporter for stdout.
    reporters: [
        'spec',
        ['json', {
            outputDir: process.env.CLIENT_ID ? `test-results/${process.env.CLIENT_ID}` : 'test-results',
            outputFileFormat: function(options) {
                return `results-${options.cid}.json`
            }
        }],
        // Video recording configured for mobile testing
        ['video', {
            saveAllVideos: true,                    // Record all tests, not just failures
            videoSlowdownMultiplier: 1,             // Real-time speed for mobile
            outputDir: process.env.CLIENT_ID ? `test-results/${process.env.CLIENT_ID}` : '_results_',
            maxTestNameCharacters: 100,
            videoRenderTimeout: 10,                 // Longer timeout for mobile
            excludeDriverActions: [],               // Include all driver actions
            // Mobile-specific video settings
            videoQuality: 'high',
            videoFPS: 15,
        }]
    ],
    
    //
    // Options to be passed to Mocha.
    mochaOpts: {
        ui: 'bdd',
        timeout: 120000 // Increased timeout for mobile operations
    },

    //
    // =====
    // Hooks
    // =====
    /**
     * Gets executed once before all workers get launched.
     */
    onPrepare: function (config, capabilities) {
        console.log('🚀 Starting mobile test execution...');
    },
    
    /**
     * Gets executed before a worker process is spawned and can be used to initialize the environment in that worker
     * and compile the TypeScript files, for example.
     */
    onWorkerStart: function (cid, caps, specs, args, execArgv) {
        console.log(`Worker ${cid} started`)
    },
    
    /**
     * Gets executed just after a worker process has exited.
     */
    onWorkerEnd: function (cid, exitCode, specs, retries) {
        console.log(`Worker ${cid} ended with exit code ${exitCode}`)
    },
    
    /**
     * Gets executed just before initialising the webdriver session and test framework.
     */
    beforeSession: function (config, capabilities, specs, cid) {
        console.log('Setting up session...')
        console.log('📱 Mobile capabilities:', JSON.stringify(capabilities, null, 2));
    },
    
    /**
     * Gets executed before test execution begins. Perfect place for global setup.
     */
    before: function (capabilities, specs) {
        // Global setup for mobile testing
        // Set implicit wait for element finding
        // browser.setTimeout({
        //     'implicit': 10000,
        //     'pageLoad': 30000,
        //     'script': 60000
        // })
    },
    
    /**
     * Runs before a WebdriverIO command gets executed.
     */
    beforeCommand: function (commandName, args) {
        // Optional: Log commands for debugging
        // console.log(`Executing: ${commandName} with args:`, args)
    },
    
    /**
     * Hook that gets executed before the suite starts
     */
    beforeSuite: function (suite) {
        console.log(`Starting suite: ${suite.title}`)
    },
    
    /**
     * Hook that gets executed _before_ a hook within the suite starts (e.g. runs before calling beforeEach in Mocha)
     */
    beforeHook: function (test, context) {
        // Setup before each test hook
    },
    
    /**
     * Hook that gets executed _after_ a hook within the suite ends (e.g. runs after calling afterEach in Mocha)
     */
    afterHook: function (test, context, { error, result, duration, passed, retries }) {
        // Cleanup after each test hook
    },
    
    /**
     * Function to be executed before a test (in Mocha/Jasmine) starts.
     */
    beforeTest: function (test, context) {
        // console.log(`Starting test: ${test.title}`)
        // Video recording will be started after app loads in the test itself
    },
    
    /**
     * Function to be executed after a test (in Mocha/Jasmine) ends.
     */
    afterTest: async function(test, context, { error, result, duration, passed, retries }) {
        if (error) {
            // Take screenshot on failure
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
            const screenshotPath = `./screenshots/failure-${timestamp}.png`
            // browser.saveScreenshot(screenshotPath)
            console.log(`Screenshot saved: ${screenshotPath}`)
        }

        // Video recording is handled by wdio-video-reporter
    },
    
    /**
     * Hook that gets executed after the suite has ended
     */
    afterSuite: function (suite) {
        console.log(`Suite completed: ${suite.title}`)
    },
    
    /**
     * Gets executed after all tests are done.
     */
    after: function (result, capabilities, specs) {
        console.log('All tests completed')
    },
    
    /**
     * Gets executed right after terminating the webdriver session.
     */
    afterSession: function (config, capabilities, specs) {
        console.log('Session terminated')
    },
    
    /**
     * Gets executed after all workers got shut down and the process is about to exit.
     */
    onComplete: function(exitCode, config, capabilities, results) {
        // console.log('✅ Appium/WebdriverIO test execution completed')
        // console.log(`Exit code: ${exitCode}`)
    },
    
    /**
     * Gets executed when a refresh happens.
     */
    onReload: function(oldSessionId, newSessionId) {
        console.log(`Session reloaded from ${oldSessionId} to ${newSessionId}`)
    }
}