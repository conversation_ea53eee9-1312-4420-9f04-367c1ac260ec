import type { Options } from '@wdio/types'
import { CapabilityFactory } from './src/services/capability-factory'
import { <PERSON>ceFarmReporter } from './src/services/device-farm-reporter'
import * as fs from 'fs'
import * as path from 'path'
import axios from 'axios'
// The 'browser' object is available globally in WebdriverIO test hooks

export const config: WebdriverIO.Config = {
    //
    // ====================
    // Runner Configuration
    // ====================
    //
    // ==================
    // Specify Test Files
    // ==================
    specs: [
        './tests/*.spec.ts'
    ],
    
    // Patterns to exclude.
    exclude: [
        // 'path/to/excluded/files'
    ],
    maxInstances: 1,
    
    //
    // ============
    // Capabilities
    // ============
    // Choose one of the following capability configurations based on your needs:
    
    // Option 1: Android Mobile Testing
    // Dynamic capabilities based on TEST_DATA environment variable
    capabilities: (() => {
        const testDataEnv = process.env.TEST_DATA;

        if (testDataEnv) {
            try {
                const testData = JSON.parse(testDataEnv);
                // console.log('🔧 wdio.conf.ts: Generating dynamic capabilities from test data');
                // console.log('🔍 wdio.conf.ts: Test data structure:', JSON.stringify(testData, null, 2));

                const dynamicCapabilities = CapabilityFactory.createCapabilities(testData.steps || []);

                if (dynamicCapabilities && CapabilityFactory.isMobilePlatform(dynamicCapabilities)) {
                    // console.log('📱 wdio.conf.ts: Using mobile capabilities');
                    // Return mobile capabilities in the format expected by wdio
                    return [{
                        ...dynamicCapabilities.capabilities,
                        // Add connection details for remote Appium server
                        hostname: dynamicCapabilities.hostname,
                        port: dynamicCapabilities.port,
                        path: dynamicCapabilities.path
                    }];
                }
            } catch (error) {
                console.error('🚫 wdio.conf.ts: Failed to parse TEST_DATA:', error);
            }
        }

        // Fallback: Return error capability to indicate mobile-only mode
        console.log('🚫 wdio.conf.ts: No valid mobile capabilities found - mobile testing required');
        return [{
            // This will cause the test to fail with a clear message
            platformName: 'MobileRequired',
            'appium:deviceName': 'Mobile testing only - setup action required'
        }];
    })(),

    //
    // ===================
    // Test Configurations
    // ===================
    logLevel: 'info',
    bail: 0,
    waitforTimeout: 10000,
    connectionRetryTimeout: 120000,
    connectionRetryCount: 0,

    // Appium server configuration for mobile testing
    hostname: 'localhost',
    port: 4723,
    path: '/wd/hub',
    
    runner: 'local',

    //
    // Test runner services
    // Disable appium service since we're using external Appium server
    services: [],
    
    //
    // Framework you want to run your specs with.
    framework: 'mocha',
    
    //
    // The number of times to retry the entire specfile when it fails as a whole
    specFileRetries: 1, // Increased for mobile stability
    
    //
    // Delay in seconds between the spec file retry attempts
    specFileRetriesDelay: 5, // Added delay for mobile
    
    //
    // Whether or not retried spec files should be retried immediately or deferred to the end of the queue
    specFileRetriesDeferred: false,
    
    //
    // Test reporter for stdout.
    reporters: [
        'spec',
        ['json', {
            outputDir: process.env.CLIENT_ID ? `test-results/${process.env.CLIENT_ID}` : 'test-results',
            outputFileFormat: function(options) {
                return `results-${options.cid}.json`
            }
        }],
        // Video recording configured for mobile testing
        ['video', {
            saveAllVideos: true,                    // Record all tests, not just failures
            videoSlowdownMultiplier: 1,             // Real-time speed for mobile
            outputDir: process.env.CLIENT_ID ? `test-results/${process.env.CLIENT_ID}` : '_results_',
            maxTestNameCharacters: 100,
            videoRenderTimeout: 10,                 // Longer timeout for mobile
            excludeDriverActions: [],               // Include all driver actions
            // Mobile-specific video settings
            videoQuality: 'high',
            videoFPS: 15,
        }]
    ],
    
    //
    // Options to be passed to Mocha.
    mochaOpts: {
        ui: 'bdd',
        timeout: 120000 // Increased timeout for mobile operations
    },

    //
    // =====
    // Hooks
    // =====
    /**
     * Gets executed once before all workers get launched.
     */
    onPrepare: function (config, capabilities) {
        console.log('🚀 Starting mobile test execution...');
        // Reset device farm reporter for new session
        DeviceFarmReporter.resetSession();

        // Add process exit handlers for device farm status
        process.on('exit', (code) => {
            console.log(`🔚 PROCESS_EXIT_CODE: ${code}`);
            const status = code === 0 ? 'PASSED' : 'FAILED';
            console.log(`🎯 PROCESS_FINAL_STATUS: ${status}`);
        });
    },
    
    /**
     * Gets executed before a worker process is spawned and can be used to initialize the environment in that worker
     * and compile the TypeScript files, for example.
     */
    onWorkerStart: function (cid, caps, specs, args, execArgv) {
        console.log(`Worker ${cid} started`)
    },
    
    /**
     * Gets executed just after a worker process has exited.
     */
    onWorkerEnd: function (cid, exitCode, specs, retries) {
        console.log(`Worker ${cid} ended with exit code ${exitCode}`)
    },
    
    /**
     * Gets executed just before initialising the webdriver session and test framework.
     */
    beforeSession: function (config, capabilities, specs, cid) {
        console.log('Setting up session...')
        console.log('📱 Mobile capabilities:', JSON.stringify(capabilities, null, 2));
    },
    
    /**
     * Gets executed before test execution begins. Perfect place for global setup.
     */
    before: function (capabilities, specs) {
        // Global setup for mobile testing
        // Set implicit wait for element finding
        // browser.setTimeout({
        //     'implicit': 10000,
        //     'pageLoad': 30000,
        //     'script': 60000
        // })
    },
    
    /**
     * Runs before a WebdriverIO command gets executed.
     */
    beforeCommand: function (commandName, args) {
        // Optional: Log commands for debugging
        // console.log(`Executing: ${commandName} with args:`, args)
    },
    
    /**
     * Hook that gets executed before the suite starts
     */
    beforeSuite: function (suite) {
        console.log(`Starting suite: ${suite.title}`)
    },
    
    /**
     * Hook that gets executed _before_ a hook within the suite starts (e.g. runs before calling beforeEach in Mocha)
     */
    beforeHook: function (test, context) {
        // Setup before each test hook
    },
    
    /**
     * Hook that gets executed _after_ a hook within the suite ends (e.g. runs after calling afterEach in Mocha)
     */
    afterHook: function (test, context, { error, result, duration, passed, retries }) {
        // Cleanup after each test hook
    },
    
    /**
     * Function to be executed before a test (in Mocha/Jasmine) starts.
     */
    beforeTest: function (test, context) {
        // console.log(`Starting test: ${test.title}`)
        // Video recording will be started after app loads in the test itself
    },
    
    /**
     * Function to be executed after a test (in Mocha/Jasmine) ends.
     */
    afterTest: async function(test, context, { error, result, duration, passed, retries }) {
        console.log(`📊 Test completed: ${test.title}, Passed: ${passed}, Duration: ${duration}ms`);

        // Device farm status logging - structured format for device farm to parse
        const testStatus = passed ? 'PASSED' : 'FAILED';
        console.log(`🏁 DEVICE_FARM_TEST_RESULT: ${test.title} = ${testStatus}`);
        console.log(`⏱️ DEVICE_FARM_TEST_DURATION: ${duration || 0}ms`);

        if (error) {
            console.log(`❌ DEVICE_FARM_TEST_ERROR: ${error.message}`);
            // Take screenshot on failure
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
            const screenshotPath = `./screenshots/failure-${timestamp}.png`
            console.log(`📸 Screenshot would be saved: ${screenshotPath}`)
        } else {
            console.log(`✅ DEVICE_FARM_TEST_SUCCESS: ${test.title}`);
        }

        // Report to device farm reporter for aggregation
        try {
            await DeviceFarmReporter.reportTestResult({
                testName: test.title,
                status: passed ? 'passed' : 'failed',
                duration: duration || 0,
                error: error ? error.message : undefined,
                timestamp: Date.now()
            });
        } catch (statusError) {
            console.log('⚠️ Error in status aggregation:', statusError);
        }

        // Video recording is handled by wdio-video-reporter
    },
    
    /**
     * Hook that gets executed after the suite has ended
     */
    afterSuite: function (suite) {
        console.log(`Suite completed: ${suite.title}`)
    },
    
    /**
     * Gets executed after all tests are done.
     */
    after: function (result, capabilities, specs) {
        console.log('All tests completed')
    },
    
    /**
     * Gets executed right after terminating the webdriver session.
     */
    afterSession: function (config, capabilities, specs) {
        console.log('Session terminated')
        // Final device farm status marker
        console.log('🔚 DEVICE_FARM_SESSION_TERMINATED');
    },
    
    /**
     * Gets executed after all workers got shut down and the process is about to exit.
     */
    onComplete: async function(exitCode, config, capabilities, results) {
        console.log('✅ Appium/WebdriverIO test execution completed')
        console.log(`Exit code: ${exitCode}`)

        // Calculate and report final status for device farm
        let totalTests = 0;
        let passedTests = 0;
        let failedTests = 0;

        console.log('🔍 Debug - Results structure:', JSON.stringify(results, null, 2));

        if (results && results.length > 0) {
            for (const result of results) {
                console.log('🔍 Debug - Processing result:', JSON.stringify(result, null, 2));

                // Handle different result structures
                if (result.specs) {
                    for (const spec of result.specs) {
                        totalTests++;
                        console.log(`🔍 Debug - Spec state: ${spec.state}`);
                        if (spec.state === 'passed') {
                            passedTests++;
                        } else if (spec.state === 'failed') {
                            failedTests++;
                        }
                    }
                } else if (result.tests) {
                    // Alternative structure
                    for (const test of result.tests) {
                        totalTests++;
                        if (test.state === 'passed') {
                            passedTests++;
                        } else if (test.state === 'failed') {
                            failedTests++;
                        }
                    }
                } else {
                    // Fallback - count the result itself
                    totalTests++;
                    if (result.state === 'passed' || exitCode === 0) {
                        passedTests++;
                    } else {
                        failedTests++;
                    }
                }
            }
        }

        // If no results detected but exit code is 0, assume 1 passed test
        if (totalTests === 0 && exitCode === 0) {
            console.log('🔍 No results detected but exit code is 0 - assuming 1 passed test');
            totalTests = 1;
            passedTests = 1;
            failedTests = 0;
        } else if (totalTests === 0 && exitCode !== 0) {
            console.log('🔍 No results detected and exit code is non-zero - assuming 1 failed test');
            totalTests = 1;
            passedTests = 0;
            failedTests = 1;
        }

        // Determine overall status based on exit code and test results
        const overallStatus = (exitCode === 0 && failedTests === 0) ? 'PASSED' : 'FAILED';
        const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(2) : '0';

        // Device farm status logging - clear markers for device farm to detect
        console.log(`🏁 DEVICE_FARM_SESSION_COMPLETE: ${overallStatus}`);
        console.log(`📊 DEVICE_FARM_TOTAL_TESTS: ${totalTests}`);
        console.log(`✅ DEVICE_FARM_PASSED_TESTS: ${passedTests}`);
        console.log(`❌ DEVICE_FARM_FAILED_TESTS: ${failedTests}`);
        console.log(`📈 DEVICE_FARM_SUCCESS_RATE: ${successRate}%`);
        console.log(`🔚 DEVICE_FARM_EXIT_CODE: ${exitCode}`);

        // Final status marker that device farm should detect
        console.log(`🎯 FINAL_TEST_STATUS: ${overallStatus}`);

        // Additional device farm markers (different formats that various farms might recognize)
        console.log(`TEST_RESULT=${overallStatus}`);
        console.log(`TEST_STATUS=${overallStatus}`);
        console.log(`APPIUM_TEST_RESULT=${overallStatus}`);
        console.log(`SESSION_STATUS=${overallStatus}`);

        // Standard test result markers
        if (overallStatus === 'PASSED') {
            console.log('✅ ALL_TESTS_PASSED');
            console.log('✅ TEST_EXECUTION_SUCCESSFUL');
        } else {
            console.log('❌ TESTS_FAILED');
            console.log('❌ TEST_EXECUTION_FAILED');
        }

        // Report to aggregator for logging
        try {
            const sessionId = Array.isArray(capabilities) && capabilities.length > 0
                ? capabilities[0]['appium:udid'] || 'unknown-session'
                : 'unknown-session';

            await DeviceFarmReporter.reportSessionComplete(sessionId);
        } catch (error) {
            console.error('⚠️ Error in status aggregation:', error);
        }
    },
    
    /**
     * Gets executed when a refresh happens.
     */
    onReload: function(oldSessionId, newSessionId) {
        console.log(`Session reloaded from ${oldSessionId} to ${newSessionId}`)
    }
}